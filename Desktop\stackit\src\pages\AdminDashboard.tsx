import React from 'react';
import { Shield, Users, MessageSquare, Tag, AlertTriangle } from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';

export function AdminDashboard() {
  const { questions, answers, users, tags } = useAppStore();
  
  const pendingQuestions = questions.filter(q => q.status === 'PENDING');
  const totalUsers = users.length;
  const totalQuestions = questions.length;
  const totalAnswers = answers.length;
  const totalTags = tags.length;

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex items-center space-x-3">
        <Shield className="w-8 h-8 text-primary-600" />
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{totalUsers}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Questions</p>
              <p className="text-2xl font-bold text-gray-900">{totalQuestions}</p>
            </div>
            <MessageSquare className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Answers</p>
              <p className="text-2xl font-bold text-gray-900">{totalAnswers}</p>
            </div>
            <MessageSquare className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tags</p>
              <p className="text-2xl font-bold text-gray-900">{totalTags}</p>
            </div>
            <Tag className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Pending Content */}
      <div className="card">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            <h2 className="text-xl font-semibold text-gray-900">
              Pending Questions ({pendingQuestions.length})
            </h2>
          </div>
        </div>
        
        {pendingQuestions.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No pending questions to review.
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {pendingQuestions.map((question) => (
              <div key={question.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {question.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">
                      by {question.author.username}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {question.tags.map((tag) => (
                        <span key={tag.id} className="badge-primary">
                          {tag.name}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <button className="btn-secondary btn-sm">
                      Approve
                    </button>
                    <button className="btn-danger btn-sm">
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Recent Questions</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {questions.slice(0, 5).map((question) => (
              <div key={question.id} className="p-4">
                <h3 className="font-medium text-gray-900 text-sm mb-1">
                  {question.title}
                </h3>
                <p className="text-xs text-gray-500">
                  by {question.author.username} • {question.answerCount} answers
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Active Users</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {users.slice(0, 5).map((user) => (
              <div key={user.id} className="p-4 flex items-center space-x-3">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.username}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gray-300 rounded-full" />
                )}
                <div className="flex-1">
                  <p className="font-medium text-gray-900 text-sm">{user.username}</p>
                  <p className="text-xs text-gray-500">{user.reputation} reputation</p>
                </div>
                <span className={`badge ${
                  user.role === 'ADMIN' ? 'badge-primary' : 'badge-secondary'
                }`}>
                  {user.role.toLowerCase()}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
