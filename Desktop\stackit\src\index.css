@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-md {
    @apply px-4 py-2 text-sm;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Input styles */
  .input {
    @apply block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:bg-gray-50 disabled:text-gray-500;
  }
  
  .input-error {
    @apply border-red-300 focus:border-red-500 focus:ring-red-500;
  }
  
  /* Card styles */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
  
  .card-hover {
    @apply card transition-all duration-200 hover:shadow-md hover:border-gray-300;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Rich text content */
  .prose {
    @apply max-w-none text-gray-900;
  }
  
  .prose h1 {
    @apply text-2xl font-bold text-gray-900 mb-4;
  }
  
  .prose h2 {
    @apply text-xl font-semibold text-gray-900 mb-3;
  }
  
  .prose h3 {
    @apply text-lg font-semibold text-gray-900 mb-2;
  }
  
  .prose h4 {
    @apply text-base font-semibold text-gray-900 mb-2;
  }
  
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  
  .prose ul {
    @apply mb-4 list-disc list-inside space-y-1;
  }
  
  .prose ol {
    @apply mb-4 list-decimal list-inside space-y-1;
  }
  
  .prose li {
    @apply leading-relaxed;
  }
  
  .prose blockquote {
    @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-4;
  }
  
  .prose pre {
    @apply bg-gray-100 rounded-lg p-4 overflow-x-auto mb-4 text-sm;
  }
  
  .prose code {
    @apply bg-gray-100 rounded px-1.5 py-0.5 text-sm font-mono;
  }
  
  .prose pre code {
    @apply bg-transparent p-0;
  }
  
  .prose a {
    @apply text-primary-600 hover:text-primary-800 underline;
  }
  
  .prose img {
    @apply rounded-lg max-w-full h-auto mb-4;
  }
  
  .prose table {
    @apply w-full border-collapse border border-gray-300 mb-4;
  }
  
  .prose th,
  .prose td {
    @apply border border-gray-300 px-3 py-2 text-left;
  }
  
  .prose th {
    @apply bg-gray-50 font-semibold;
  }
  
  /* Scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
}
