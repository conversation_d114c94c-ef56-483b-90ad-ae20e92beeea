import React, { useState, useRef, useEffect } from 'react';
import { X } from 'lucide-react';
import { Tag } from '@/types';

interface TagInputProps {
  selectedTags: string[];
  onChange: (tags: string[]) => void;
  availableTags: Tag[];
  maxTags?: number;
  placeholder?: string;
}

export function TagInput({ 
  selectedTags, 
  onChange, 
  availableTags, 
  maxTags = 5, 
  placeholder 
}: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<Tag[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeSuggestion, setActiveSuggestion] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputValue.length > 0) {
      const filtered = availableTags
        .filter(tag => 
          tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
          !selectedTags.includes(tag.name)
        )
        .slice(0, 5);
      setSuggestions(filtered);
      setShowSuggestions(true);
      setActiveSuggestion(-1);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
    }
  }, [inputValue, selectedTags, availableTags]);

  const addTag = (tagName: string) => {
    const trimmedTag = tagName.trim().toLowerCase();
    if (trimmedTag && !selectedTags.includes(trimmedTag) && selectedTags.length < maxTags) {
      onChange([...selectedTags, trimmedTag]);
    }
    setInputValue('');
    setShowSuggestions(false);
    setActiveSuggestion(-1);
  };

  const removeTag = (tagToRemove: string) => {
    onChange(selectedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (activeSuggestion >= 0 && suggestions[activeSuggestion]) {
        addTag(suggestions[activeSuggestion].name);
      } else if (inputValue.trim()) {
        addTag(inputValue);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveSuggestion(prev => 
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setActiveSuggestion(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setActiveSuggestion(-1);
    } else if (e.key === 'Backspace' && !inputValue && selectedTags.length > 0) {
      removeTag(selectedTags[selectedTags.length - 1]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSuggestionClick = (tag: Tag) => {
    addTag(tag.name);
    inputRef.current?.focus();
  };

  return (
    <div className="relative">
      <div className="min-h-[42px] p-2 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-transparent">
        <div className="flex flex-wrap gap-2">
          {selectedTags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
            >
              {tag}
              <button
                type="button"
                onClick={() => removeTag(tag)}
                className="ml-1 text-primary-600 hover:text-primary-800"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
          {selectedTags.length < maxTags && (
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={selectedTags.length === 0 ? placeholder : ''}
              className="flex-1 min-w-[120px] outline-none bg-transparent"
            />
          )}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion.id}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full px-3 py-2 text-left hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg ${
                index === activeSuggestion ? 'bg-primary-50 text-primary-700' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <span>{suggestion.name}</span>
                <span className="text-xs text-gray-500">
                  {suggestion.questionCount} questions
                </span>
              </div>
              {suggestion.description && (
                <div className="text-xs text-gray-500 mt-1 truncate">
                  {suggestion.description}
                </div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Tag count indicator */}
      <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
        <span>Press Enter to add tags</span>
        <span>{selectedTags.length}/{maxTags} tags</span>
      </div>
    </div>
  );
}
