import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthContextType, RegisterForm } from '@/types';
import { mockUsers } from '@/data/mockUsers';
import { useLocalStorage } from '@/hooks/useLocalStorage';

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  loading: true,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, loading: false, error: null };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'LOGOUT':
      return { ...state, user: null, loading: false, error: null };
    default:
      return state;
  }
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const [storedUser, setStoredUser, removeStoredUser] = useLocalStorage<User | null>('stackit_user', null);

  useEffect(() => {
    // Simulate checking for existing session
    const checkAuth = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (storedUser) {
        // Verify user still exists in our mock data
        const currentUser = mockUsers.find(u => u.id === storedUser.id);
        if (currentUser && !currentUser.banned) {
          dispatch({ type: 'SET_USER', payload: currentUser });
        } else {
          removeStoredUser();
          dispatch({ type: 'SET_USER', payload: null });
        }
      } else {
        dispatch({ type: 'SET_USER', payload: null });
      }
    };

    checkAuth();
  }, [storedUser, removeStoredUser]);

  const login = async (email: string, password: string): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Find user by email
    const user = mockUsers.find(u => u.email === email);
    
    if (!user) {
      dispatch({ type: 'SET_ERROR', payload: 'Invalid email or password' });
      return false;
    }

    if (user.banned) {
      dispatch({ type: 'SET_ERROR', payload: 'Your account has been banned' });
      return false;
    }

    // In a real app, you'd verify the password hash
    // For demo purposes, accept any password
    if (password.length < 6) {
      dispatch({ type: 'SET_ERROR', payload: 'Invalid email or password' });
      return false;
    }

    setStoredUser(user);
    dispatch({ type: 'SET_USER', payload: user });
    return true;
  };

  const register = async (data: RegisterForm): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if email already exists
    const existingUser = mockUsers.find(u => u.email === data.email);
    if (existingUser) {
      dispatch({ type: 'SET_ERROR', payload: 'Email already exists' });
      return false;
    }

    // Check if username already exists
    const existingUsername = mockUsers.find(u => u.username === data.username);
    if (existingUsername) {
      dispatch({ type: 'SET_ERROR', payload: 'Username already exists' });
      return false;
    }

    // Create new user
    const newUser: User = {
      id: `user_${Date.now()}`,
      email: data.email,
      username: data.username,
      role: 'USER',
      bio: '',
      createdAt: new Date().toISOString(),
      banned: false,
      reputation: 0,
      questionsCount: 0,
      answersCount: 0,
    };

    // Add to mock users (in a real app, this would be saved to the database)
    mockUsers.push(newUser);

    setStoredUser(newUser);
    dispatch({ type: 'SET_USER', payload: newUser });
    return true;
  };

  const logout = () => {
    removeStoredUser();
    dispatch({ type: 'LOGOUT' });
  };

  const value: AuthContextType = {
    user: state.user,
    login,
    register,
    logout,
    isAuthenticated: !!state.user,
    isAdmin: state.user?.role === 'ADMIN',
    loading: state.loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
