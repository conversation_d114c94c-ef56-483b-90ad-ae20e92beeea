// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(USER)
  avatar    String?
  bio       String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  banned    Boolean  @default(false)
  banReason String?

  // Relations
  questions     Question[]
  answers       Answer[]
  votes         Vote[]
  notifications Notification[]
  reports       Report[]
  sessions      Session[]
  accounts      Account[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Question {
  id          String   @id @default(cuid())
  title       String
  description String
  authorId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  status      QuestionStatus @default(PENDING)

  // Relations
  author      User       @relation(fields: [authorId], references: [id], onDelete: Cascade)
  answers     Answer[]
  tags        QuestionTag[]
  reports     Report[]

  @@map("questions")
}

model Answer {
  id         String   @id @default(cuid())
  content    String
  questionId String
  authorId   String
  isAccepted Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  status     AnswerStatus @default(PENDING)

  // Relations
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  author   User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  votes    Vote[]
  reports  Report[]

  @@map("answers")
}

model Tag {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?
  createdAt   DateTime @default(now())

  // Relations
  questions QuestionTag[]

  @@map("tags")
}

model QuestionTag {
  questionId String
  tagId      String

  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  tag      Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([questionId, tagId])
  @@map("question_tags")
}

model Vote {
  id       String   @id @default(cuid())
  userId   String
  answerId String
  type     VoteType
  createdAt DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  answer Answer @relation(fields: [answerId], references: [id], onDelete: Cascade)

  @@unique([userId, answerId])
  @@map("votes")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  data      String?          // JSON data for additional context
  createdAt DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Report {
  id         String     @id @default(cuid())
  reporterId String
  reason     String
  status     ReportStatus @default(PENDING)
  createdAt  DateTime   @default(now())
  resolvedAt DateTime?
  
  // Polymorphic relations
  questionId String?
  answerId   String?

  // Relations
  reporter User      @relation(fields: [reporterId], references: [id], onDelete: Cascade)
  question Question? @relation(fields: [questionId], references: [id], onDelete: Cascade)
  answer   Answer?   @relation(fields: [answerId], references: [id], onDelete: Cascade)

  @@map("reports")
}

// Enums
enum UserRole {
  GUEST
  USER
  ADMIN
}

enum QuestionStatus {
  PENDING
  APPROVED
  REJECTED
}

enum AnswerStatus {
  PENDING
  APPROVED
  REJECTED
}

enum VoteType {
  UP
  DOWN
}

enum NotificationType {
  ANSWER_RECEIVED
  COMMENT_RECEIVED
  MENTION_RECEIVED
  ANSWER_ACCEPTED
  ADMIN_MESSAGE
}

enum ReportStatus {
  PENDING
  RESOLVED
  DISMISSED
}
