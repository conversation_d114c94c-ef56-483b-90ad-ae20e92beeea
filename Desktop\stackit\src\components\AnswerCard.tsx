import React from 'react';
import { Link } from 'react-router-dom';
import { ThumbsUp, ThumbsDown, Check, Clock, Edit, Trash2, Flag } from 'lucide-react';
import { AnswerCardProps } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { useAppStore } from '@/store/useAppStore';
import { formatDistanceToNow } from 'date-fns';

export function AnswerCard({ answer, questionAuthorId, showActions = true }: AnswerCardProps) {
  const { user, isAuthenticated } = useAuth();
  const { voteAnswer, acceptAnswer } = useAppStore();

  const isAuthor = user?.id === answer.authorId;
  const isQuestionAuthor = user?.id === questionAuthorId;
  const canEdit = isAuthor || user?.role === 'ADMIN';
  const canAccept = isQuestionAuthor && !answer.isAccepted;

  const handleVote = (voteType: 'UP' | 'DOWN') => {
    if (!user || isAuthor) return;
    voteAnswer(answer.id, user.id, voteType);
  };

  const handleAccept = () => {
    if (!canAccept) return;
    acceptAnswer(answer.id, answer.questionId);
  };

  return (
    <div className={`p-6 ${answer.isAccepted ? 'bg-green-50 border-l-4 border-green-500' : ''}`}>
      <div className="flex space-x-4">
        {/* Vote buttons */}
        <div className="flex flex-col items-center space-y-2 flex-shrink-0">
          <button
            onClick={() => handleVote('UP')}
            disabled={!isAuthenticated || isAuthor}
            className={`p-2 rounded-full transition-colors ${
              answer.userVote === 'UP'
                ? 'bg-green-100 text-green-600'
                : 'hover:bg-gray-100 text-gray-600'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <ThumbsUp className="w-5 h-5" />
          </button>
          
          <span className={`text-lg font-semibold ${
            answer.score > 0 ? 'text-green-600' :
            answer.score < 0 ? 'text-red-600' :
            'text-gray-900'
          }`}>
            {answer.score}
          </span>
          
          <button
            onClick={() => handleVote('DOWN')}
            disabled={!isAuthenticated || isAuthor}
            className={`p-2 rounded-full transition-colors ${
              answer.userVote === 'DOWN'
                ? 'bg-red-100 text-red-600'
                : 'hover:bg-gray-100 text-gray-600'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <ThumbsDown className="w-5 h-5" />
          </button>
          
          {answer.isAccepted && (
            <div className="bg-green-100 text-green-600 p-2 rounded-full">
              <Check className="w-5 h-5" />
            </div>
          )}
          
          {canAccept && (
            <button
              onClick={handleAccept}
              className="p-2 rounded-full text-gray-400 hover:text-green-600 hover:bg-green-100 transition-colors"
              title="Accept this answer"
            >
              <Check className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Answer content */}
        <div className="flex-1 min-w-0">
          <div 
            className="prose max-w-none mb-4"
            dangerouslySetInnerHTML={{ __html: answer.content }}
          />

          {/* Meta info and actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>
                  answered {formatDistanceToNow(new Date(answer.createdAt), { addSuffix: true })}
                </span>
              </div>
              <Link
                to={`/users/${answer.author.username}`}
                className="flex items-center space-x-2 hover:text-gray-700 transition-colors"
              >
                {answer.author.avatar ? (
                  <img
                    src={answer.author.avatar}
                    alt={answer.author.username}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-6 h-6 bg-gray-300 rounded-full" />
                )}
                <span>{answer.author.username}</span>
                {answer.author.role === 'ADMIN' && (
                  <span className="badge-primary text-xs">Admin</span>
                )}
              </Link>
            </div>

            {/* Actions */}
            {showActions && isAuthenticated && (
              <div className="flex items-center space-x-2">
                {canEdit && (
                  <button className="flex items-center space-x-1 text-gray-600 hover:text-primary-600 transition-colors text-sm">
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </button>
                )}
                {canEdit && (
                  <button className="flex items-center space-x-1 text-gray-600 hover:text-red-600 transition-colors text-sm">
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </button>
                )}
                {!isAuthor && (
                  <button className="flex items-center space-x-1 text-gray-600 hover:text-orange-600 transition-colors text-sm">
                    <Flag className="w-4 h-4" />
                    <span>Report</span>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
