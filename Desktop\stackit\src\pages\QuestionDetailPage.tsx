import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, Eye, Clock, Edit, Trash2, Flag } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAppStore } from '@/store/useAppStore';
import { AnswerCard } from '@/components/AnswerCard';
import { AnswerForm } from '@/components/AnswerForm';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { formatDistanceToNow } from 'date-fns';

export function QuestionDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { getQuestionById, getAnswersByQuestionId, incrementQuestionViews } = useAppStore();
  
  const [question, setQuestion] = useState(id ? getQuestionById(id) : null);
  const [answers, setAnswers] = useState(id ? getAnswersByQuestionId(id) : []);

  useEffect(() => {
    if (id) {
      const q = getQuestionById(id);
      if (q) {
        setQuestion(q);
        setAnswers(getAnswersByQuestionId(id));
        incrementQuestionViews(id);
      }
    }
  }, [id, getQuestionById, getAnswersByQuestionId, incrementQuestionViews]);

  if (!question) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Question not found</h2>
        <p className="text-gray-600 mb-8">The question you're looking for doesn't exist or has been removed.</p>
        <Link to="/" className="btn-primary">
          Back to Home
        </Link>
      </div>
    );
  }

  const isAuthor = user?.id === question.authorId;
  const canEdit = isAuthor || user?.role === 'ADMIN';

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Back button */}
      <button
        onClick={() => navigate(-1)}
        className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>Back</span>
      </button>

      {/* Question */}
      <div className="card p-6">
        <div className="flex items-start justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 flex-1 mr-4">
            {question.title}
          </h1>
          <span className={`badge ${
            question.status === 'APPROVED' ? 'badge-success' :
            question.status === 'PENDING' ? 'badge-warning' :
            'badge-danger'
          }`}>
            {question.status.toLowerCase()}
          </span>
        </div>

        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-6">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>
              asked {formatDistanceToNow(new Date(question.createdAt), { addSuffix: true })}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{question.viewCount} views</span>
          </div>
          <Link
            to={`/users/${question.author.username}`}
            className="flex items-center space-x-2 hover:text-gray-700 transition-colors"
          >
            {question.author.avatar ? (
              <img
                src={question.author.avatar}
                alt={question.author.username}
                className="w-6 h-6 rounded-full object-cover"
              />
            ) : (
              <div className="w-6 h-6 bg-gray-300 rounded-full" />
            )}
            <span>{question.author.username}</span>
            {question.author.role === 'ADMIN' && (
              <span className="badge-primary text-xs">Admin</span>
            )}
          </Link>
        </div>

        <div 
          className="prose max-w-none mb-6"
          dangerouslySetInnerHTML={{ __html: question.description }}
        />

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-6">
          {question.tags.map((tag) => (
            <Link
              key={tag.id}
              to={`/?tag=${encodeURIComponent(tag.name)}`}
              className="badge-primary hover:bg-primary-200 transition-colors"
              style={{ backgroundColor: tag.color ? `${tag.color}20` : undefined }}
            >
              {tag.name}
            </Link>
          ))}
        </div>

        {/* Actions */}
        {isAuthenticated && (
          <div className="flex items-center space-x-4 pt-4 border-t border-gray-200">
            {canEdit && (
              <button className="flex items-center space-x-1 text-gray-600 hover:text-primary-600 transition-colors">
                <Edit className="w-4 h-4" />
                <span>Edit</span>
              </button>
            )}
            {canEdit && (
              <button className="flex items-center space-x-1 text-gray-600 hover:text-red-600 transition-colors">
                <Trash2 className="w-4 h-4" />
                <span>Delete</span>
              </button>
            )}
            {!isAuthor && (
              <button className="flex items-center space-x-1 text-gray-600 hover:text-orange-600 transition-colors">
                <Flag className="w-4 h-4" />
                <span>Report</span>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Answers */}
      <div className="card">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {answers.length} Answer{answers.length !== 1 ? 's' : ''}
          </h2>
        </div>

        {answers.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No answers yet. Be the first to answer!
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {answers.map((answer) => (
              <AnswerCard
                key={answer.id}
                answer={answer}
                questionAuthorId={question.authorId}
              />
            ))}
          </div>
        )}
      </div>

      {/* Answer Form */}
      {isAuthenticated ? (
        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Your Answer</h3>
          </div>
          <div className="p-6">
            <AnswerForm questionId={question.id} />
          </div>
        </div>
      ) : (
        <div className="card p-6 text-center">
          <p className="text-gray-600 mb-4">
            You need to be logged in to post an answer.
          </p>
          <button className="btn-primary">
            Sign In to Answer
          </button>
        </div>
      )}
    </div>
  );
}
