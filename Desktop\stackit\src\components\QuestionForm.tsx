import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { RichTextEditor } from './RichTextEditor';
import { TagInput } from './TagInput';
import { useAppStore } from '@/store/useAppStore';

const questionSchema = z.object({
  title: z.string()
    .min(10, 'Title must be at least 10 characters')
    .max(200, 'Title must be less than 200 characters'),
  description: z.string()
    .min(20, 'Description must be at least 20 characters'),
  tags: z.array(z.string())
    .min(1, 'At least one tag is required')
    .max(5, 'Maximum 5 tags allowed'),
});

interface QuestionFormProps {
  onSubmit: (data: any) => void;
}

export function QuestionForm({ onSubmit }: QuestionFormProps) {
  const { tags } = useAppStore();
  const [description, setDescription] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      title: '',
      description: '',
      tags: [],
    },
  });

  // Watch title for character count
  const title = watch('title');

  const handleFormSubmit = async (data: any) => {
    setIsSubmitting(true);
    
    try {
      const formattedTags = selectedTags.map(tagName => {
        const existingTag = tags.find(t => t.name === tagName);
        return existingTag || {
          id: `tag_${Date.now()}_${tagName}`,
          name: tagName,
          description: '',
          questionCount: 0,
          createdAt: new Date().toISOString(),
        };
      });

      await onSubmit({
        ...data,
        description,
        tags: formattedTags,
      });
    } catch (error) {
      console.error('Failed to submit question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update form values when state changes
  React.useEffect(() => {
    setValue('description', description);
    setValue('tags', selectedTags);
  }, [description, selectedTags, setValue]);

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
          Title *
        </label>
        <input
          {...register('title')}
          type="text"
          id="title"
          className={`input ${errors.title ? 'input-error' : ''}`}
          placeholder="What's your programming question? Be specific."
        />
        <div className="flex justify-between items-center mt-1">
          {errors.title && (
            <p className="text-red-600 text-sm">{errors.title.message}</p>
          )}
          <p className="text-sm text-gray-500 ml-auto">
            {title?.length || 0}/200 characters
          </p>
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <RichTextEditor
          content={description}
          onChange={setDescription}
          placeholder="Provide details about your question. Include what you've tried and what specific help you need."
          minHeight="300px"
        />
        {errors.description && (
          <p className="text-red-600 text-sm mt-1">{errors.description.message}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Include all the information someone would need to answer your question
        </p>
      </div>

      {/* Tags */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Tags *
        </label>
        <TagInput
          selectedTags={selectedTags}
          onChange={setSelectedTags}
          availableTags={tags}
          maxTags={5}
          placeholder="Add up to 5 tags to describe what your question is about"
        />
        {errors.tags && (
          <p className="text-red-600 text-sm mt-1">{errors.tags.message}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Add tags to help others find and answer your question
        </p>
      </div>

      {/* Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          Writing a good question
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Make your title specific and descriptive</li>
          <li>• Explain the problem clearly in the description</li>
          <li>• Include relevant code, error messages, or examples</li>
          <li>• Add appropriate tags to categorize your question</li>
          <li>• Show what you've already tried</li>
        </ul>
      </div>

      {/* Submit Button */}
      <div className="flex items-center justify-end space-x-4">
        <button
          type="button"
          onClick={() => window.history.back()}
          className="btn-secondary"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting || !title || !description || selectedTags.length === 0}
          className="btn-primary"
        >
          {isSubmitting ? 'Publishing...' : 'Publish Question'}
        </button>
      </div>
    </form>
  );
}
