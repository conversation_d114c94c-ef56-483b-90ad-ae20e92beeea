import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Filter, TrendingUp } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAppStore } from '@/store/useAppStore';
import { QuestionCard } from '@/components/QuestionCard';
import { TagFilter } from '@/components/TagFilter';
import { LoadingSpinner } from '@/components/LoadingSpinner';

export function HomePage() {
  const { isAuthenticated } = useAuth();
  const {
    getQuestions,
    getPopularTags,
    searchQuery,
    selectedTags,
    sortBy,
    setSortBy,
    loading
  } = useAppStore();

  const [questions, setQuestions] = useState(getQuestions());
  const [popularTags] = useState(getPopularTags(8));

  useEffect(() => {
    setQuestions(getQuestions());
  }, [searchQuery, selectedTags, sortBy, getQuestions]);

  const sortOptions = [
    { value: 'newest', label: 'Newest' },
    { value: 'oldest', label: 'Oldest' },
    { value: 'most-answered', label: 'Most Answered' },
    { value: 'most-voted', label: 'Most Voted' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome to StackIt
          </h1>
          <p className="text-gray-600 mt-1">
            A collaborative Q&A platform for developers and learners
          </p>
        </div>
        {isAuthenticated && (
          <Link
            to="/ask"
            className="btn-primary btn-lg flex items-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Ask Question</span>
          </Link>
        )}
      </div>

      {/* Popular Tags */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-4">
          <TrendingUp className="w-5 h-5 text-primary-600" />
          <h2 className="text-lg font-semibold text-gray-900">Popular Tags</h2>
        </div>
        <div className="flex flex-wrap gap-2">
          {popularTags.map((tag) => (
            <TagFilter key={tag.id} tag={tag} />
          ))}
        </div>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600">
            {questions.length} question{questions.length !== 1 ? 's' : ''}
            {selectedTags.length > 0 && (
              <span> with tags: {selectedTags.join(', ')}</span>
            )}
            {searchQuery && (
              <span> matching "{searchQuery}"</span>
            )}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="input text-sm py-1 px-2 w-auto"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Questions List */}
      {questions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            {searchQuery || selectedTags.length > 0
              ? 'No questions found matching your criteria'
              : 'No questions yet. Be the first to ask!'
            }
          </div>
          {isAuthenticated && (
            <Link
              to="/ask"
              className="btn-primary flex items-center space-x-2 mx-auto w-fit"
            >
              <Plus className="w-4 h-4" />
              <span>Ask the First Question</span>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {questions.map((question) => (
            <QuestionCard key={question.id} question={question} />
          ))}
        </div>
      )}
    </div>
  );
}
