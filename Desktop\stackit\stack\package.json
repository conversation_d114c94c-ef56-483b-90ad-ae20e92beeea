{"name": "stackit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5", "@prisma/client": "^5.7.0", "prisma": "^5.7.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "next-auth": "^4.24.5", "@next-auth/prisma-adapter": "^1.0.7", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-text-align": "^2.1.13", "@tiptap/extension-list-item": "^2.1.13", "@tiptap/extension-bullet-list": "^2.1.13", "@tiptap/extension-ordered-list": "^2.1.13", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-badge": "^0.2.3", "@radix-ui/react-button": "^0.1.0", "@radix-ui/react-input": "^0.1.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-textarea": "^0.1.0", "@radix-ui/react-tabs": "^1.0.4"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5"}}