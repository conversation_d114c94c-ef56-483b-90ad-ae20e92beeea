import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { SearchBarProps } from '@/types';

export function SearchBar({ value, onChange, placeholder, onSubmit }: SearchBarProps) {
  const [localValue, setLocalValue] = useState(value);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onChange(localValue);
    onSubmit?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value);
    // Debounced search - update parent after user stops typing
    const timeoutId = setTimeout(() => {
      onChange(e.target.value);
    }, 300);
    
    return () => clearTimeout(timeoutId);
  };

  return (
    <form onSubmit={handleSubmit} className="relative w-full">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          value={localValue}
          onChange={handleChange}
          placeholder={placeholder}
          className="input pl-10 pr-4"
        />
      </div>
    </form>
  );
}
