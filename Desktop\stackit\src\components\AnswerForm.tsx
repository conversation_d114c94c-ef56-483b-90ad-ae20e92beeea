import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useAppStore } from '@/store/useAppStore';
import { RichTextEditor } from './RichTextEditor';

interface AnswerFormProps {
  questionId: string;
}

export function AnswerForm({ questionId }: AnswerFormProps) {
  const { user } = useAuth();
  const { addAnswer } = useAppStore();
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !content.trim()) {
      setError('Please provide an answer');
      return;
    }

    if (content.length < 10) {
      setError('Answer must be at least 10 characters long');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      addAnswer({
        content,
        questionId,
        authorId: user.id,
        author: user,
        isAccepted: false,
        status: user.role === 'ADMIN' ? 'APPROVED' : 'PENDING',
      });

      setContent('');
      // Show success message or redirect
    } catch (err) {
      setError('Failed to submit answer. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Your Answer
        </label>
        <RichTextEditor
          content={content}
          onChange={setContent}
          placeholder="Write your answer here..."
          minHeight="200px"
        />
      </div>

      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-500">
          Minimum 10 characters required
        </p>
        <button
          type="submit"
          disabled={isSubmitting || content.length < 10}
          className="btn-primary"
        >
          {isSubmitting ? 'Submitting...' : 'Post Answer'}
        </button>
      </div>
    </form>
  );
}
