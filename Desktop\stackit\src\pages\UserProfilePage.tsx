import React from 'react';
import { useParams } from 'react-router-dom';
import { Calendar, MapPin, Link as LinkIcon, Award } from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';
import { formatDistanceToNow } from 'date-fns';

export function UserProfilePage() {
  const { username } = useParams<{ username: string }>();
  const { users, getQuestions } = useAppStore();
  
  const user = users.find(u => u.username === username);
  const userQuestions = user ? getQuestions().filter(q => q.authorId === user.id) : [];

  if (!user) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">User not found</h2>
        <p className="text-gray-600">The user you're looking for doesn't exist.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Profile Header */}
      <div className="card p-6">
        <div className="flex items-start space-x-6">
          {user.avatar ? (
            <img
              src={user.avatar}
              alt={user.username}
              className="w-24 h-24 rounded-full object-cover"
            />
          ) : (
            <div className="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-2xl font-bold text-gray-600">
                {user.username.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{user.username}</h1>
              <span className={`badge ${
                user.role === 'ADMIN' ? 'badge-primary' : 'badge-secondary'
              }`}>
                {user.role.toLowerCase()}
              </span>
              {user.banned && (
                <span className="badge-danger">Banned</span>
              )}
            </div>
            
            {user.bio && (
              <p className="text-gray-600 mb-4">{user.bio}</p>
            )}
            
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>
                  Joined {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Award className="w-4 h-4" />
                <span>{user.reputation} reputation</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-primary-600 mb-2">
            {user.questionsCount}
          </div>
          <div className="text-gray-600">Questions Asked</div>
        </div>
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-green-600 mb-2">
            {user.answersCount}
          </div>
          <div className="text-gray-600">Answers Given</div>
        </div>
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-orange-600 mb-2">
            {user.reputation}
          </div>
          <div className="text-gray-600">Reputation</div>
        </div>
      </div>

      {/* Recent Questions */}
      <div className="card">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Recent Questions</h2>
        </div>
        {userQuestions.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No questions asked yet.
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {userQuestions.slice(0, 5).map((question) => (
              <div key={question.id} className="p-6">
                <h3 className="font-medium text-gray-900 mb-2">
                  <a href={`/questions/${question.id}`} className="hover:text-primary-600">
                    {question.title}
                  </a>
                </h3>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>{question.answerCount} answers</span>
                  <span>{question.viewCount} views</span>
                  <span>
                    {formatDistanceToNow(new Date(question.createdAt), { addSuffix: true })}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
