import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { db } from './db'

export async function hashPassword(password) {
  return await bcrypt.hash(password, 12)
}

export async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword)
}

export function generateToken(payload) {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '7d' })
}

export function verifyToken(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET)
  } catch (error) {
    return null
  }
}

export async function getCurrentUser(request) {
  try {
    const token = request.cookies.get('auth-token')?.value
    if (!token) return null

    const decoded = verifyToken(token)
    if (!decoded) return null

    const user = await db.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        avatar: true,
        banned: true
      }
    })

    if (user?.banned) return null
    return user
  } catch (error) {
    return null
  }
}

export function requireAuth(handler) {
  return async (request, context) => {
    const user = await getCurrentUser(request)
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    return handler(request, { ...context, user })
  }
}

export function requireAdmin(handler) {
  return async (request, context) => {
    const user = await getCurrentUser(request)
    if (!user || user.role !== 'ADMIN') {
      return new Response(JSON.stringify({ error: 'Forbidden' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    return handler(request, { ...context, user })
  }
}
